#include "sierrachart.h"
#include <vector>
#include <map>

SCDLLName("Trapped Trader Zones")

// Structure to store cluster information
struct s_AggressiveCluster
{
    int BarIndex;
    float HighPrice;
    float LowPrice;
    float DominantPrice;
    unsigned int BidVolume;
    unsigned int AskVolume;
    bool IsBullishAggression;  // true if AskVolume >> BidVolume
    bool IsConfirmed;          // true if failure confirmed
    int ConfirmationBars;      // count of opposite bars
    SCDateTime ClusterTime;
    
    s_AggressiveCluster()
    {
        BarIndex = 0;
        HighPrice = 0;
        LowPrice = 0;
        DominantPrice = 0;
        BidVolume = 0;
        AskVolume = 0;
        IsBullishAggression = false;
        IsConfirmed = false;
        ConfirmationBars = 0;
    }
};

// Forward declarations
void AnalyzeVolumeAtPrice(SCStudyInterfaceRef& sc, int BarIndex, std::vector<s_AggressiveCluster>& Clusters);
bool DetectAggressiveCluster(SCStudyInterfaceRef& sc, int BarIndex, s_AggressiveCluster& Cluster);
void ConfirmTrappedTraders(SCStudyInterfaceRef& sc, std::vector<s_AggressiveCluster>& Clusters);
void DrawTrappedZones(SCStudyInterfaceRef& sc, const std::vector<s_AggressiveCluster>& Clusters);
void ManageZoneLifecycle(SCStudyInterfaceRef& sc, std::vector<s_AggressiveCluster>& Clusters);

/*==========================================================================*/
SCSFExport scsf_TrappedTraderZones(SCStudyInterfaceRef sc)
{
    SCSubgraphRef BullishZones = sc.Subgraph[0];
    SCSubgraphRef BearishZones = sc.Subgraph[1];
    SCSubgraphRef ClusterStrength = sc.Subgraph[2];
    
    SCInputRef AggressionRatio = sc.Input[0];
    SCInputRef MinVolumeThreshold = sc.Input[1];
    SCInputRef ConfirmationBars = sc.Input[2];
    SCInputRef ZoneExtensionBars = sc.Input[3];
    SCInputRef BullishZoneColor = sc.Input[4];
    SCInputRef BearishZoneColor = sc.Input[5];
    
    if (sc.SetDefaults)
    {
        sc.GraphName = "Trapped Trader Zones";
        sc.StudyDescription = "Identifies trapped trader zones based on aggressive bid/ask volume clusters that fail to sustain price movement";
        
        sc.AutoLoop = 0;  // Manual looping for complex analysis
        sc.GraphRegion = 0;
        sc.ValueFormat = 2;
        sc.ScaleRangeType = SCALE_SAMEASREGION;
        
        // Subgraph settings
        BullishZones.Name = "Bullish Trapped Zones";
        BullishZones.DrawStyle = DRAWSTYLE_TRANSPARENT_FILL_RECTANGLE_TOP;
        BullishZones.PrimaryColor = RGB(255, 0, 0);  // Red for trapped bulls
        BullishZones.SecondaryColor = RGB(255, 0, 0);
        BullishZones.SecondaryColorUsed = true;
        BullishZones.LineWidth = 2;
        
        BearishZones.Name = "Bearish Trapped Zones";
        BearishZones.DrawStyle = DRAWSTYLE_TRANSPARENT_FILL_RECTANGLE_BOTTOM;
        BearishZones.PrimaryColor = RGB(0, 255, 0);  // Green for trapped bears
        BearishZones.SecondaryColor = RGB(0, 255, 0);
        BearishZones.SecondaryColorUsed = true;
        BearishZones.LineWidth = 2;
        
        ClusterStrength.Name = "Cluster Strength";
        ClusterStrength.DrawStyle = DRAWSTYLE_IGNORE;
        
        // Input settings
        AggressionRatio.Name = "Aggression Ratio (e.g., 3.0 means 3:1 volume ratio)";
        AggressionRatio.SetFloat(2.5f);
        AggressionRatio.SetFloatLimits(1.5f, 10.0f);
        
        MinVolumeThreshold.Name = "Minimum Volume Threshold";
        MinVolumeThreshold.SetInt(100);
        MinVolumeThreshold.SetIntLimits(10, 10000);
        
        ConfirmationBars.Name = "Confirmation Bars (opposite direction)";
        ConfirmationBars.SetInt(2);
        ConfirmationBars.SetIntLimits(1, 5);
        
        ZoneExtensionBars.Name = "Zone Extension Bars";
        ZoneExtensionBars.SetInt(50);
        ZoneExtensionBars.SetIntLimits(10, 200);
        
        BullishZoneColor.Name = "Bullish Zone Color";
        BullishZoneColor.SetColor(RGB(255, 100, 100));
        
        BearishZoneColor.Name = "Bearish Zone Color";
        BearishZoneColor.SetColor(RGB(100, 255, 100));
        
        return;
    }
    
    // Check if volume at price data is available
    if (sc.VolumeAtPriceForBars == nullptr)
    {
        sc.AddMessageToLog("Volume at Price data not available. Enable 'Intraday Data Storage Time Unit' in Chart Settings.", 1);
        return;
    }
    
    // Static storage for clusters across function calls
    static std::vector<s_AggressiveCluster> Clusters;
    
    // Clear old clusters on full recalculation
    if (sc.IsFullRecalculation)
    {
        Clusters.clear();
    }
    
    // Process bars from update start index
    for (int BarIndex = sc.UpdateStartIndex; BarIndex < sc.ArraySize; BarIndex++)
    {
        // Analyze volume at price for this bar
        AnalyzeVolumeAtPrice(sc, BarIndex, Clusters);
        
        // Confirm trapped traders for existing clusters
        ConfirmTrappedTraders(sc, Clusters);
        
        // Manage zone lifecycle (remove expired zones)
        ManageZoneLifecycle(sc, Clusters);
        
        // Draw zones
        DrawTrappedZones(sc, Clusters);
    }
}

/*==========================================================================*/
void AnalyzeVolumeAtPrice(SCStudyInterfaceRef& sc, int BarIndex, std::vector<s_AggressiveCluster>& Clusters)
{
    s_AggressiveCluster NewCluster;
    
    if (DetectAggressiveCluster(sc, BarIndex, NewCluster))
    {
        // Add new cluster to our collection
        Clusters.push_back(NewCluster);
        
        SCString Message;
        Message.Format("Aggressive cluster detected at bar %d, Price: %.2f, %s aggression", 
                      BarIndex, NewCluster.DominantPrice, 
                      NewCluster.IsBullishAggression ? "Bullish" : "Bearish");
        sc.AddMessageToLog(Message, 0);
    }
}

/*==========================================================================*/
bool DetectAggressiveCluster(SCStudyInterfaceRef& sc, int BarIndex, s_AggressiveCluster& Cluster)
{
    if (sc.VolumeAtPriceForBars == nullptr)
        return false;
    
    float BarHigh = sc.High[BarIndex];
    float BarLow = sc.Low[BarIndex];
    float TickSize = sc.TickSize;
    
    // Convert prices to ticks for volume analysis
    int HighTick = sc.PriceValueToTicks(BarHigh);
    int LowTick = sc.PriceValueToTicks(BarLow);
    
    unsigned int TotalBidVolume = 0;
    unsigned int TotalAskVolume = 0;
    unsigned int MaxBidAtPrice = 0;
    unsigned int MaxAskAtPrice = 0;
    float DominantPrice = 0;
    
    // Analyze volume at each price level within the bar
    for (int PriceTick = LowTick; PriceTick <= HighTick; PriceTick++)
    {
        unsigned int BidVol = sc.VolumeAtPriceForBars->GetBidVolumeAtPrice(BarIndex, PriceTick);
        unsigned int AskVol = sc.VolumeAtPriceForBars->GetAskVolumeAtPrice(BarIndex, PriceTick);
        
        TotalBidVolume += BidVol;
        TotalAskVolume += AskVol;
        
        // Track the price level with maximum volume imbalance
        if (BidVol > MaxBidAtPrice)
        {
            MaxBidAtPrice = BidVol;
            if (AskVol == 0 || (BidVol > AskVol * sc.Input[0].GetFloat()))
                DominantPrice = sc.TicksToPrice(PriceTick);
        }
        
        if (AskVol > MaxAskAtPrice)
        {
            MaxAskAtPrice = AskVol;
            if (BidVol == 0 || (AskVol > BidVol * sc.Input[0].GetFloat()))
                DominantPrice = sc.TicksToPrice(PriceTick);
        }
    }
    
    // Check for aggressive cluster conditions
    float AggressionRatio = sc.Input[0].GetFloat();
    int MinVolume = sc.Input[1].GetInt();
    
    bool IsBullishAggression = (TotalAskVolume > TotalBidVolume * AggressionRatio) && 
                               (TotalAskVolume >= MinVolume);
    bool IsBearishAggression = (TotalBidVolume > TotalAskVolume * AggressionRatio) && 
                               (TotalBidVolume >= MinVolume);
    
    if (IsBullishAggression || IsBearishAggression)
    {
        // Fill cluster information
        Cluster.BarIndex = BarIndex;
        Cluster.HighPrice = BarHigh;
        Cluster.LowPrice = BarLow;
        Cluster.DominantPrice = DominantPrice;
        Cluster.BidVolume = TotalBidVolume;
        Cluster.AskVolume = TotalAskVolume;
        Cluster.IsBullishAggression = IsBullishAggression;
        Cluster.IsConfirmed = false;
        Cluster.ConfirmationBars = 0;
        Cluster.ClusterTime = sc.BaseDateTimeIn[BarIndex];
        
        return true;
    }
    
    return false;
}

/*==========================================================================*/
void ConfirmTrappedTraders(SCStudyInterfaceRef& sc, std::vector<s_AggressiveCluster>& Clusters)
{
    int ConfirmationBarsNeeded = sc.Input[2].GetInt();

    for (size_t i = 0; i < Clusters.size(); i++)
    {
        s_AggressiveCluster& cluster = Clusters[i];

        // Skip already confirmed clusters
        if (cluster.IsConfirmed)
            continue;

        // Check bars after the cluster for failure confirmation
        int BarsToCheck = min(sc.ArraySize - cluster.BarIndex - 1, ConfirmationBarsNeeded + 2);
        int OppositeBars = 0;

        for (int j = 1; j <= BarsToCheck; j++)
        {
            int CheckIndex = cluster.BarIndex + j;
            if (CheckIndex >= sc.ArraySize)
                break;

            float ClosePrice = sc.Close[CheckIndex];
            float OpenPrice = sc.Open[CheckIndex];

            if (cluster.IsBullishAggression)
            {
                // For bullish aggression, look for failure to extend higher and bearish closes
                if (ClosePrice < OpenPrice && sc.High[CheckIndex] <= cluster.HighPrice * 1.001f)
                {
                    OppositeBars++;
                }
            }
            else
            {
                // For bearish aggression, look for failure to extend lower and bullish closes
                if (ClosePrice > OpenPrice && sc.Low[CheckIndex] >= cluster.LowPrice * 0.999f)
                {
                    OppositeBars++;
                }
            }
        }

        // Confirm if we have enough opposite bars
        if (OppositeBars >= ConfirmationBarsNeeded)
        {
            cluster.IsConfirmed = true;
            cluster.ConfirmationBars = OppositeBars;

            SCString Message;
            Message.Format("Trapped traders confirmed at %.2f - %s aggression failed",
                          cluster.DominantPrice,
                          cluster.IsBullishAggression ? "Bullish" : "Bearish");
            sc.AddMessageToLog(Message, 0);
        }
    }
}

/*==========================================================================*/
void DrawTrappedZones(SCStudyInterfaceRef& sc, const std::vector<s_AggressiveCluster>& Clusters)
{
    // Clear existing drawings
    sc.DeleteACSChartDrawing(sc.ChartNumber, TOOL_DELETE_ALL, 0);

    int ZoneExtensionBars = sc.Input[3].GetInt();

    for (size_t i = 0; i < Clusters.size(); i++)
    {
        const s_AggressiveCluster& cluster = Clusters[i];

        // Only draw confirmed trapped zones
        if (!cluster.IsConfirmed)
            continue;

        // Calculate zone extension
        int EndBarIndex = min(cluster.BarIndex + ZoneExtensionBars, sc.ArraySize - 1);

        // Check if zone has been touched (price has returned to zone)
        bool ZoneTouched = false;
        for (int j = cluster.BarIndex + 1; j <= EndBarIndex; j++)
        {
            if (cluster.IsBullishAggression)
            {
                // Check if price returned to the trapped bull zone
                if (sc.Low[j] <= cluster.HighPrice && sc.High[j] >= cluster.LowPrice)
                {
                    ZoneTouched = true;
                    EndBarIndex = j;
                    break;
                }
            }
            else
            {
                // Check if price returned to the trapped bear zone
                if (sc.High[j] >= cluster.LowPrice && sc.Low[j] <= cluster.HighPrice)
                {
                    ZoneTouched = true;
                    EndBarIndex = j;
                    break;
                }
            }
        }

        // Create rectangle drawing for the zone
        s_UseTool Rectangle;
        Rectangle.Clear();
        Rectangle.ChartNumber = sc.ChartNumber;
        Rectangle.DrawingType = DRAWING_RECTANGLEHIGHLIGHT;
        Rectangle.AddAsUserDrawnDrawing = 0;
        Rectangle.BeginIndex = cluster.BarIndex;
        Rectangle.EndIndex = EndBarIndex;
        Rectangle.BeginValue = cluster.HighPrice;
        Rectangle.EndValue = cluster.LowPrice;
        Rectangle.Color = cluster.IsBullishAggression ? sc.Input[4].GetColor() : sc.Input[5].GetColor();
        Rectangle.SecondaryColor = Rectangle.Color;
        Rectangle.LineWidth = 2;
        Rectangle.TransparencyLevel = 75;

        // Add text label
        SCString ZoneText;
        ZoneText.Format("%s Trap %.2f",
                       cluster.IsBullishAggression ? "Bull" : "Bear",
                       cluster.DominantPrice);
        Rectangle.Text = ZoneText;
        Rectangle.FontSize = 8;

        sc.UseTool(Rectangle);
    }
}

/*==========================================================================*/
void ManageZoneLifecycle(SCStudyInterfaceRef& sc, std::vector<s_AggressiveCluster>& Clusters)
{
    int MaxZoneAge = sc.Input[3].GetInt() * 2;  // Double the extension bars for max age

    // Remove old or invalid clusters
    for (auto it = Clusters.begin(); it != Clusters.end();)
    {
        int ClusterAge = sc.ArraySize - 1 - it->BarIndex;

        // Remove if too old or if zone has been invalidated
        if (ClusterAge > MaxZoneAge)
        {
            it = Clusters.erase(it);
        }
        else
        {
            ++it;
        }
    }

    // Limit total number of clusters to prevent memory issues
    const size_t MaxClusters = 50;
    if (Clusters.size() > MaxClusters)
    {
        // Remove oldest clusters
        Clusters.erase(Clusters.begin(), Clusters.begin() + (Clusters.size() - MaxClusters));
    }
}
