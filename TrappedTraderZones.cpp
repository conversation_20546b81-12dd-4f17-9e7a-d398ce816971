#include "sierrachart.h"
#include <vector>
#include <map>

SCDLLName("Trapped Trader Zones")

// Structure to store cluster information
struct s_AggressiveCluster
{
    int BarIndex;
    float HighPrice;
    float LowPrice;
    float DominantPrice;
    unsigned int BidVolume;
    unsigned int AskVolume;
    bool IsBullishAggression;  // true if AskVolume >> BidVolume
    bool IsConfirmed;          // true if failure confirmed
    int ConfirmationBars;      // count of opposite bars
    SCDateTime ClusterTime;
    int DrawingNumber;         // Track drawing for updates
    bool ZoneTouched;          // Track if zone has been hit
    int TouchBarIndex;         // Bar where zone was touched
    float VolumeImbalanceRatio; // Store the actual ratio for strength

    s_AggressiveCluster()
    {
        BarIndex = 0;
        HighPrice = 0;
        LowPrice = 0;
        DominantPrice = 0;
        BidVolume = 0;
        AskVolume = 0;
        IsBullishAggression = false;
        IsConfirmed = false;
        ConfirmationBars = 0;
        DrawingNumber = 0;
        ZoneTouched = false;
        TouchBarIndex = 0;
        VolumeImbalanceRatio = 0;
    }
};

// Forward declarations
void AnalyzeVolumeAtPrice(SCStudyInterfaceRef& sc, int BarIndex, std::vector<s_AggressiveCluster>& Clusters);
bool DetectAggressiveCluster(SCStudyInterfaceRef& sc, int BarIndex, s_AggressiveCluster& Cluster);
void ConfirmTrappedTraders(SCStudyInterfaceRef& sc, std::vector<s_AggressiveCluster>& Clusters);
void CheckZoneTouches(SCStudyInterfaceRef& sc, std::vector<s_AggressiveCluster>& Clusters, int CurrentBarIndex);
void UpdateSubgraphs(SCStudyInterfaceRef& sc, const std::vector<s_AggressiveCluster>& Clusters, int BarIndex);
void DrawTrappedZones(SCStudyInterfaceRef& sc, const std::vector<s_AggressiveCluster>& Clusters);
void ManageZoneLifecycle(SCStudyInterfaceRef& sc, std::vector<s_AggressiveCluster>& Clusters);

/*==========================================================================*/
SCSFExport scsf_TrappedTraderZones(SCStudyInterfaceRef sc)
{
    SCSubgraphRef BullishZones = sc.Subgraph[0];
    SCSubgraphRef BearishZones = sc.Subgraph[1];
    SCSubgraphRef ClusterStrength = sc.Subgraph[2];
    SCSubgraphRef ZoneAlerts = sc.Subgraph[3];

    SCInputRef AggressionRatio = sc.Input[0];
    SCInputRef MinVolumeThreshold = sc.Input[1];
    SCInputRef ConfirmationBars = sc.Input[2];
    SCInputRef ZoneExtensionBars = sc.Input[3];
    SCInputRef BullishZoneColor = sc.Input[4];
    SCInputRef BearishZoneColor = sc.Input[5];
    SCInputRef ShowZoneLabels = sc.Input[6];
    SCInputRef AlertOnZoneTouch = sc.Input[7];
    SCInputRef MinStrengthFilter = sc.Input[8];
    
    if (sc.SetDefaults)
    {
        sc.GraphName = "Trapped Trader Zones";
        sc.StudyDescription = "Identifies trapped trader zones based on aggressive bid/ask volume clusters that fail to sustain price movement";
        
        sc.AutoLoop = 0;  // Manual looping for complex analysis
        sc.GraphRegion = 0;
        sc.ValueFormat = 2;
        sc.ScaleRangeType = SCALE_SAMEASREGION;

        // Create a second study region for cluster strength
        sc.CreateStudySubgraphsRegion(1, 1);
        
        // Subgraph settings
        BullishZones.Name = "Bullish Trapped Zones";
        BullishZones.DrawStyle = DRAWSTYLE_TRANSPARENT_FILL_RECTANGLE_TOP;
        BullishZones.PrimaryColor = RGB(255, 0, 0);  // Red for trapped bulls
        BullishZones.SecondaryColor = RGB(255, 0, 0);
        BullishZones.SecondaryColorUsed = true;
        BullishZones.LineWidth = 2;
        
        BearishZones.Name = "Bearish Trapped Zones";
        BearishZones.DrawStyle = DRAWSTYLE_TRANSPARENT_FILL_RECTANGLE_BOTTOM;
        BearishZones.PrimaryColor = RGB(0, 255, 0);  // Green for trapped bears
        BearishZones.SecondaryColor = RGB(0, 255, 0);
        BearishZones.SecondaryColorUsed = true;
        BearishZones.LineWidth = 2;
        
        ClusterStrength.Name = "Cluster Strength";
        ClusterStrength.DrawStyle = DRAWSTYLE_BAR;
        ClusterStrength.PrimaryColor = RGB(128, 128, 128);
        ClusterStrength.GraphRegion = 1;

        ZoneAlerts.Name = "Zone Touch Alerts";
        ZoneAlerts.DrawStyle = DRAWSTYLE_POINT;
        ZoneAlerts.PrimaryColor = RGB(255, 255, 0);
        ZoneAlerts.LineWidth = 3;

        // Input settings
        AggressionRatio.Name = "Aggression Ratio (e.g., 3.0 means 3:1 volume ratio)";
        AggressionRatio.SetFloat(2.5f);
        AggressionRatio.SetFloatLimits(1.5f, 10.0f);

        MinVolumeThreshold.Name = "Minimum Volume Threshold";
        MinVolumeThreshold.SetInt(100);
        MinVolumeThreshold.SetIntLimits(10, 10000);

        ConfirmationBars.Name = "Confirmation Bars (opposite direction)";
        ConfirmationBars.SetInt(2);
        ConfirmationBars.SetIntLimits(1, 5);

        ZoneExtensionBars.Name = "Zone Extension Bars";
        ZoneExtensionBars.SetInt(50);
        ZoneExtensionBars.SetIntLimits(10, 200);

        BullishZoneColor.Name = "Bullish Zone Color (Trapped Bulls)";
        BullishZoneColor.SetColor(RGB(255, 100, 100));

        BearishZoneColor.Name = "Bearish Zone Color (Trapped Bears)";
        BearishZoneColor.SetColor(RGB(100, 255, 100));

        ShowZoneLabels.Name = "Show Zone Labels";
        ShowZoneLabels.SetYesNo(1);

        AlertOnZoneTouch.Name = "Alert When Zone Touched";
        AlertOnZoneTouch.SetYesNo(0);

        MinStrengthFilter.Name = "Minimum Strength Filter (0=All, 5=Strong only)";
        MinStrengthFilter.SetFloat(0.0f);
        MinStrengthFilter.SetFloatLimits(0.0f, 10.0f);
        
        return;
    }
    
    // Check if volume at price data is available
    if (sc.VolumeAtPriceForBars == nullptr)
    {
        sc.AddMessageToLog("Volume at Price data not available. Enable 'Intraday Data Storage Time Unit' in Chart Settings.", 1);
        return;
    }
    
    // Static storage for clusters across function calls
    static std::vector<s_AggressiveCluster> Clusters;
    
    // Clear old clusters on full recalculation
    if (sc.IsFullRecalculation)
    {
        Clusters.clear();
    }
    
    // Initialize subgraph arrays
    for (int i = 0; i < sc.ArraySize; i++)
    {
        BullishZones[i] = 0;
        BearishZones[i] = 0;
        ClusterStrength[i] = 0;
        ZoneAlerts[i] = 0;
    }

    // Process bars from update start index
    for (int BarIndex = sc.UpdateStartIndex; BarIndex < sc.ArraySize; BarIndex++)
    {
        // Analyze volume at price for this bar
        AnalyzeVolumeAtPrice(sc, BarIndex, Clusters);

        // Confirm trapped traders for existing clusters
        ConfirmTrappedTraders(sc, Clusters);

        // Check for zone touches and alerts
        CheckZoneTouches(sc, Clusters, BarIndex);

        // Update subgraph values
        UpdateSubgraphs(sc, Clusters, BarIndex);
    }

    // Manage zone lifecycle (remove expired zones) - do this after processing
    ManageZoneLifecycle(sc, Clusters);

    // Draw all zones
    DrawTrappedZones(sc, Clusters);
}

/*==========================================================================*/
void AnalyzeVolumeAtPrice(SCStudyInterfaceRef& sc, int BarIndex, std::vector<s_AggressiveCluster>& Clusters)
{
    s_AggressiveCluster NewCluster;
    
    if (DetectAggressiveCluster(sc, BarIndex, NewCluster))
    {
        // Add new cluster to our collection
        Clusters.push_back(NewCluster);
        
        SCString Message;
        Message.Format("Aggressive cluster detected at bar %d, Price: %.2f, %s aggression", 
                      BarIndex, NewCluster.DominantPrice, 
                      NewCluster.IsBullishAggression ? "Bullish" : "Bearish");
        sc.AddMessageToLog(Message, 0);
    }
}

/*==========================================================================*/
bool DetectAggressiveCluster(SCStudyInterfaceRef& sc, int BarIndex, s_AggressiveCluster& Cluster)
{
    if (sc.VolumeAtPriceForBars == nullptr)
        return false;
    
    float BarHigh = sc.High[BarIndex];
    float BarLow = sc.Low[BarIndex];
    float TickSize = sc.TickSize;
    
    // Convert prices to ticks for volume analysis
    int HighTick = sc.PriceValueToTicks(BarHigh);
    int LowTick = sc.PriceValueToTicks(BarLow);
    
    unsigned int TotalBidVolume = 0;
    unsigned int TotalAskVolume = 0;
    unsigned int MaxBidAtPrice = 0;
    unsigned int MaxAskAtPrice = 0;
    float DominantPrice = 0;
    
    // Analyze volume at each price level within the bar
    for (int PriceTick = LowTick; PriceTick <= HighTick; PriceTick++)
    {
        unsigned int BidVol = sc.VolumeAtPriceForBars->GetBidVolumeAtPrice(BarIndex, PriceTick);
        unsigned int AskVol = sc.VolumeAtPriceForBars->GetAskVolumeAtPrice(BarIndex, PriceTick);
        
        TotalBidVolume += BidVol;
        TotalAskVolume += AskVol;
        
        // Track the price level with maximum volume imbalance
        if (BidVol > MaxBidAtPrice)
        {
            MaxBidAtPrice = BidVol;
            if (AskVol == 0 || (BidVol > AskVol * sc.Input[0].GetFloat()))
                DominantPrice = sc.TicksToPriceValue(PriceTick);
        }

        if (AskVol > MaxAskAtPrice)
        {
            MaxAskAtPrice = AskVol;
            if (BidVol == 0 || (AskVol > BidVol * sc.Input[0].GetFloat()))
                DominantPrice = sc.TicksToPriceValue(PriceTick);
        }
    }
    
    // Check for aggressive cluster conditions
    float AggressionRatio = sc.Input[0].GetFloat();
    int MinVolume = sc.Input[1].GetInt();

    bool IsBullishAggression = (TotalAskVolume > TotalBidVolume * AggressionRatio) &&
                               (TotalAskVolume >= MinVolume);
    bool IsBearishAggression = (TotalBidVolume > TotalAskVolume * AggressionRatio) &&
                               (TotalBidVolume >= MinVolume);

    if (IsBullishAggression || IsBearishAggression)
    {
        // Calculate volume imbalance ratio for strength measurement
        float VolumeRatio = IsBullishAggression ?
            (float)TotalAskVolume / (float)max(TotalBidVolume, 1u) :
            (float)TotalBidVolume / (float)max(TotalAskVolume, 1u);

        // Fill cluster information
        Cluster.BarIndex = BarIndex;
        Cluster.HighPrice = BarHigh;
        Cluster.LowPrice = BarLow;
        Cluster.DominantPrice = (DominantPrice > 0) ? DominantPrice : (BarHigh + BarLow) / 2.0f;
        Cluster.BidVolume = TotalBidVolume;
        Cluster.AskVolume = TotalAskVolume;
        Cluster.IsBullishAggression = IsBullishAggression;
        Cluster.IsConfirmed = false;
        Cluster.ConfirmationBars = 0;
        Cluster.ClusterTime = sc.BaseDateTimeIn[BarIndex];
        Cluster.VolumeImbalanceRatio = VolumeRatio;
        Cluster.ZoneTouched = false;
        Cluster.TouchBarIndex = 0;

        return true;
    }

    return false;
}

/*==========================================================================*/
void ConfirmTrappedTraders(SCStudyInterfaceRef& sc, std::vector<s_AggressiveCluster>& Clusters)
{
    int ConfirmationBarsNeeded = sc.Input[2].GetInt();

    for (size_t i = 0; i < Clusters.size(); i++)
    {
        s_AggressiveCluster& cluster = Clusters[i];

        // Skip already confirmed clusters
        if (cluster.IsConfirmed)
            continue;

        // Check bars after the cluster for failure confirmation
        int BarsToCheck = min(sc.ArraySize - cluster.BarIndex - 1, ConfirmationBarsNeeded + 2);
        int OppositeBars = 0;

        for (int j = 1; j <= BarsToCheck; j++)
        {
            int CheckIndex = cluster.BarIndex + j;
            if (CheckIndex >= sc.ArraySize)
                break;

            float ClosePrice = sc.Close[CheckIndex];
            float OpenPrice = sc.Open[CheckIndex];

            if (cluster.IsBullishAggression)
            {
                // For bullish aggression, look for failure to extend higher and bearish closes
                if (ClosePrice < OpenPrice && sc.High[CheckIndex] <= cluster.HighPrice * 1.001f)
                {
                    OppositeBars++;
                }
            }
            else
            {
                // For bearish aggression, look for failure to extend lower and bullish closes
                if (ClosePrice > OpenPrice && sc.Low[CheckIndex] >= cluster.LowPrice * 0.999f)
                {
                    OppositeBars++;
                }
            }
        }

        // Confirm if we have enough opposite bars
        if (OppositeBars >= ConfirmationBarsNeeded)
        {
            cluster.IsConfirmed = true;
            cluster.ConfirmationBars = OppositeBars;

            SCString Message;
            Message.Format("Trapped traders confirmed at %.2f - %s aggression failed",
                          cluster.DominantPrice,
                          cluster.IsBullishAggression ? "Bullish" : "Bearish");
            sc.AddMessageToLog(Message, 0);
        }
    }
}

/*==========================================================================*/
void CheckZoneTouches(SCStudyInterfaceRef& sc, std::vector<s_AggressiveCluster>& Clusters, int CurrentBarIndex)
{
    float CurrentHigh = sc.High[CurrentBarIndex];
    float CurrentLow = sc.Low[CurrentBarIndex];

    for (size_t i = 0; i < Clusters.size(); i++)
    {
        s_AggressiveCluster& cluster = Clusters[i];

        // Skip if not confirmed or already touched
        if (!cluster.IsConfirmed || cluster.ZoneTouched)
            continue;

        // Skip if this is the same bar as the cluster
        if (CurrentBarIndex <= cluster.BarIndex)
            continue;

        // Check if current bar touches the zone
        bool ZoneTouched = false;

        if (cluster.IsBullishAggression)
        {
            // For bullish trapped zones, check if price returned to the zone
            if (CurrentLow <= cluster.HighPrice && CurrentHigh >= cluster.LowPrice)
            {
                ZoneTouched = true;
            }
        }
        else
        {
            // For bearish trapped zones, check if price returned to the zone
            if (CurrentHigh >= cluster.LowPrice && CurrentLow <= cluster.HighPrice)
            {
                ZoneTouched = true;
            }
        }

        if (ZoneTouched)
        {
            cluster.ZoneTouched = true;
            cluster.TouchBarIndex = CurrentBarIndex;

            // Alert if enabled
            if (sc.Input[7].GetYesNo())
            {
                SCString AlertMessage;
                AlertMessage.Format("%s Trapped Zone Touched at %.2f",
                                  cluster.IsBullishAggression ? "Bullish" : "Bearish",
                                  cluster.DominantPrice);
                sc.AddMessageToLog(AlertMessage, 1);
                sc.SetAlert(1, AlertMessage);
            }
        }
    }
}

/*==========================================================================*/
void UpdateSubgraphs(SCStudyInterfaceRef& sc, const std::vector<s_AggressiveCluster>& Clusters, int BarIndex)
{
    // Reset values
    sc.Subgraph[0][BarIndex] = 0;  // BullishZones
    sc.Subgraph[1][BarIndex] = 0;  // BearishZones
    sc.Subgraph[2][BarIndex] = 0;  // ClusterStrength
    sc.Subgraph[3][BarIndex] = 0;  // ZoneAlerts

    // Check if current bar has any active zones
    for (size_t i = 0; i < Clusters.size(); i++)
    {
        const s_AggressiveCluster& cluster = Clusters[i];

        // Skip unconfirmed clusters
        if (!cluster.IsConfirmed)
            continue;

        // Check if this bar is within an active zone
        bool InZone = false;
        int ZoneExtension = sc.Input[3].GetInt();

        if (BarIndex >= cluster.BarIndex &&
            BarIndex <= cluster.BarIndex + ZoneExtension &&
            !cluster.ZoneTouched)
        {
            InZone = true;
        }

        if (InZone)
        {
            if (cluster.IsBullishAggression)
            {
                sc.Subgraph[0][BarIndex] = cluster.HighPrice;  // Mark bullish zone
            }
            else
            {
                sc.Subgraph[1][BarIndex] = cluster.LowPrice;   // Mark bearish zone
            }

            // Set cluster strength
            sc.Subgraph[2][BarIndex] = cluster.VolumeImbalanceRatio;
        }

        // Mark zone touches
        if (cluster.TouchBarIndex == BarIndex)
        {
            sc.Subgraph[3][BarIndex] = cluster.DominantPrice;
        }
    }
}

/*==========================================================================*/
void DrawTrappedZones(SCStudyInterfaceRef& sc, const std::vector<s_AggressiveCluster>& Clusters)
{
    // Clear existing drawings only on full recalculation
    if (sc.IsFullRecalculation)
    {
        sc.DeleteACSChartDrawing(sc.ChartNumber, TOOL_DELETE_ALL, 0);
    }

    int ZoneExtensionBars = sc.Input[3].GetInt();
    bool ShowLabels = sc.Input[6].GetYesNo();
    float MinStrength = sc.Input[8].GetFloat();

    for (size_t i = 0; i < Clusters.size(); i++)
    {
        const s_AggressiveCluster& cluster = Clusters[i];

        // Only draw confirmed trapped zones
        if (!cluster.IsConfirmed)
            continue;

        // Filter by minimum strength if specified
        if (MinStrength > 0 && cluster.VolumeImbalanceRatio < MinStrength)
            continue;

        // Calculate zone extension
        int EndBarIndex = cluster.ZoneTouched ?
            cluster.TouchBarIndex :
            min(cluster.BarIndex + ZoneExtensionBars, sc.ArraySize - 1);

        // Create rectangle drawing for the zone
        s_UseTool Rectangle;
        Rectangle.Clear();
        Rectangle.ChartNumber = sc.ChartNumber;
        Rectangle.DrawingType = DRAWING_RECTANGLEHIGHLIGHT;
        Rectangle.AddAsUserDrawnDrawing = 0;
        Rectangle.BeginIndex = cluster.BarIndex;
        Rectangle.EndIndex = EndBarIndex;
        Rectangle.BeginValue = cluster.HighPrice;
        Rectangle.EndValue = cluster.LowPrice;

        // Set colors based on zone type and status
        COLORREF ZoneColor;
        if (cluster.IsBullishAggression)
        {
            ZoneColor = cluster.ZoneTouched ? RGB(200, 50, 50) : sc.Input[4].GetColor();
        }
        else
        {
            ZoneColor = cluster.ZoneTouched ? RGB(50, 200, 50) : sc.Input[5].GetColor();
        }

        Rectangle.Color = ZoneColor;
        Rectangle.SecondaryColor = ZoneColor;
        Rectangle.LineWidth = cluster.ZoneTouched ? 1 : 2;
        Rectangle.TransparencyLevel = cluster.ZoneTouched ? 90 : 75;

        // Add text label if enabled
        if (ShowLabels)
        {
            SCString ZoneText;
            ZoneText.Format("%s Trap %.2f (%.1fx)",
                           cluster.IsBullishAggression ? "Bull" : "Bear",
                           cluster.DominantPrice,
                           cluster.VolumeImbalanceRatio);
            Rectangle.Text = ZoneText;
            Rectangle.FontSize = 8;
            Rectangle.FontBackColor = RGB(255, 255, 255);
            Rectangle.FontBold = 1;
        }

        // Set unique line number for tracking
        Rectangle.LineNumber = 10000 + (int)i;

        sc.UseTool(Rectangle);

        // Add horizontal line at dominant price for precision
        s_UseTool HorizontalLine;
        HorizontalLine.Clear();
        HorizontalLine.ChartNumber = sc.ChartNumber;
        HorizontalLine.DrawingType = DRAWING_HORIZONTALLINE;
        HorizontalLine.AddAsUserDrawnDrawing = 0;
        HorizontalLine.BeginIndex = cluster.BarIndex;
        HorizontalLine.EndIndex = EndBarIndex;
        HorizontalLine.BeginValue = cluster.DominantPrice;
        HorizontalLine.EndValue = cluster.DominantPrice;
        HorizontalLine.Color = cluster.IsBullishAggression ? RGB(255, 0, 0) : RGB(0, 255, 0);
        HorizontalLine.LineWidth = 1;
        HorizontalLine.LineStyle = LINESTYLE_DOT;
        HorizontalLine.LineNumber = 20000 + (int)i;

        sc.UseTool(HorizontalLine);
    }
}

/*==========================================================================*/
void ManageZoneLifecycle(SCStudyInterfaceRef& sc, std::vector<s_AggressiveCluster>& Clusters)
{
    int ZoneExtensionBars = sc.Input[3].GetInt();
    int MaxZoneAge = ZoneExtensionBars * 3;  // Triple the extension bars for max age
    int CurrentBarIndex = sc.ArraySize - 1;

    // Remove old, touched, or invalid clusters
    for (auto it = Clusters.begin(); it != Clusters.end();)
    {
        int ClusterAge = CurrentBarIndex - it->BarIndex;
        bool ShouldRemove = false;

        // Remove if too old
        if (ClusterAge > MaxZoneAge)
        {
            ShouldRemove = true;
        }

        // Remove if zone was touched and some time has passed
        if (it->ZoneTouched && (CurrentBarIndex - it->TouchBarIndex) > 10)
        {
            ShouldRemove = true;
        }

        // Remove if unconfirmed and old
        if (!it->IsConfirmed && ClusterAge > 20)
        {
            ShouldRemove = true;
        }

        if (ShouldRemove)
        {
            // Log removal for debugging
            SCString Message;
            Message.Format("Removing %s zone at %.2f (Age: %d bars, Touched: %s)",
                          it->IsBullishAggression ? "Bullish" : "Bearish",
                          it->DominantPrice, ClusterAge,
                          it->ZoneTouched ? "Yes" : "No");
            sc.AddMessageToLog(Message, 0);

            it = Clusters.erase(it);
        }
        else
        {
            ++it;
        }
    }

    // Limit total number of clusters to prevent memory issues
    const size_t MaxClusters = 30;
    if (Clusters.size() > MaxClusters)
    {
        // Remove oldest unconfirmed clusters first
        for (auto it = Clusters.begin(); it != Clusters.end() && Clusters.size() > MaxClusters;)
        {
            if (!it->IsConfirmed)
            {
                it = Clusters.erase(it);
            }
            else
            {
                ++it;
            }
        }

        // If still too many, remove oldest confirmed clusters
        if (Clusters.size() > MaxClusters)
        {
            size_t ToRemove = Clusters.size() - MaxClusters;
            Clusters.erase(Clusters.begin(), Clusters.begin() + ToRemove);
        }
    }

    // Log current status
    if (sc.ArraySize > 0 && (sc.ArraySize - 1) % 100 == 0)  // Every 100 bars
    {
        int ConfirmedZones = 0;
        int ActiveZones = 0;

        for (const auto& cluster : Clusters)
        {
            if (cluster.IsConfirmed)
                ConfirmedZones++;
            if (!cluster.ZoneTouched)
                ActiveZones++;
        }

        SCString StatusMessage;
        StatusMessage.Format("Zone Status: Total=%d, Confirmed=%d, Active=%d",
                           (int)Clusters.size(), ConfirmedZones, ActiveZones);
        sc.AddMessageToLog(StatusMessage, 0);
    }
}
